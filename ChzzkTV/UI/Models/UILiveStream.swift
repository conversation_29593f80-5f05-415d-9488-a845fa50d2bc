import Foundation

struct UILiveStream: Identifiable, Thumbnail {
    let id: Int
    let title: String
    let imageUrl: URL?
    let viewerCount: Int
    let timestamp: TimeInterval
    let category: String?
    var channel: UIChannel?
    var qualities: [VideoQuality]?
    let isAbroad: Bool
    let isAdult: Bool

    init(id: Int, title: String, imageUrl: URL?, viewerCount: Int, timestamp: TimeInterval, category: String?, channel: UIChannel?, qualities: [VideoQuality]? = nil, isAbroad: Bool = false, isAdult: Bool = false) {
        self.id = id
        self.title = title
        self.imageUrl = imageUrl
        self.viewerCount = viewerCount
        self.timestamp = timestamp
        self.category = category
        self.channel = channel
        self.qualities = qualities
        self.isAbroad = isAbroad
        self.isAdult = isAdult
    }
} 

extension UILiveStream {
    var viewerCountFormatted: String {
        let formatted = CountFormatter.abbreviated(from: viewerCount)
        return String(format: NSLocalizedString("%@ watching", comment: "Number of concurrent viewers"), formatted)
    }
    
    var relativeDate: String {
        let date = Date(timeIntervalSince1970: timestamp)
        return date.relativeString()
    }
}
