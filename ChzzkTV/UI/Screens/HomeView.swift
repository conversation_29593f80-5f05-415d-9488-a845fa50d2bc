import SwiftUI
import SwiftData

struct HomeView: View {    
    @Environment(\.scenePhase) private var scenePhase
    @Environment(\.modelContext) private var modelContext
    @Environment(\.channelService) private var channelService
    @Environment(\.videoService) private var videoService
    
    @StateObject private var viewModel: HomeViewModel
    @State private var selectedLiveStream: UILiveStream? = nil
    @State private var selectedVideo: UIVideo? = nil
    @State private var isViewActive = false
    #if os(tvOS)
    @State private var previousFocusSection: HomeSection?
    @FocusState private var focusSection: HomeSection?
    
    enum HomeSection {
        case live
        case vod
        case followers
    }
    #endif
    
    init(
        channelService: ChannelServiceProtocol,
        liveService: LiveServiceProtocol,
        videoService: VideoServiceProtocol,
        loginViewModel: QRCodeLoginViewModel
    ) {
        let viewModel = HomeViewModel(
            channelService: channelService,
            liveService: liveService,
            videoService: videoService,
            loginViewModel: loginViewModel
        )
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var body: some View {
        ZStack {
            if case .loading = viewModel.state {
                ProgressView()
                    .scaleEffect(2.0)
                    .progressViewStyle(.circular)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                ScrollView {
                    VStack(alignment: .leading, spacing: 30) {
                        contentView
                    }
                    .padding(.vertical)
                }
            }
        }
        .task {
            await viewModel.loadContent()
            #if os(tvOS)
            // Set initial focus to live section if available
            if !viewModel.followerLives.isEmpty {
                focusSection = .live
            }
            #endif
        }
        .onAppear {
            isViewActive = true
        }
        .onDisappear {
            isViewActive = false
        }
        .onChange(of: scenePhase) { _, newPhase in
            if newPhase == .active && isViewActive {
                Task {
                    await viewModel.loadContent()
                }
            }
        }
        .fullScreenCover(item: $selectedLiveStream) { stream in
            LiveDetailView(stream: stream, viewModel: .init(
                channelService: channelService
            ))
        }
        .fullScreenCover(item: $selectedVideo) { video in
            VideoDetailView(video: video, viewModel: .init(
                videoService: videoService
            ))
        }
        .onChange(of: selectedVideo) { _, newValue in
            if newValue != nil {
                #if os(tvOS)
                // Store current focus when presenting
                previousFocusSection = focusSection
                #endif
            } else {
                // Restore previous focus when dismissed
                Task {
                    await viewModel.loadVideos()
                    #if os(tvOS)
                    focusSection = previousFocusSection
                    #endif
                }
            }
        }
    }
    
    @ViewBuilder
    private var contentView: some View {
        if case .error(let error) = viewModel.state {
            ErrorView(
                title: "Something Went Wrong",
                message: error.localizedDescription,
                isRetrying: false) {
                Task {
                    await viewModel.loadContent()
                }
            }
        } else if case .loaded = viewModel.state, viewModel.isEmpty {
            EmptyHomeView()
        } else {
            contentSections
        }
    }
    
    @ViewBuilder
    private var contentSections: some View {
        if !viewModel.followerLives.isEmpty {
            VStack(alignment: .leading) {
                Text("Live Now")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.horizontal)
                
                LiveStreamHScrollView(
                    streams: viewModel.followerLives,
                    selectedStream: $selectedLiveStream,
                    onUnfollowed: { channel in
                        viewModel.unfollow(channel)
                    }
                )
            }
            #if os(tvOS)
            .focused($focusSection, equals: .live)
            #endif
        }
        
        if !viewModel.followerVideos.isEmpty {
            VStack(alignment: .leading) {
                Text("Latest Videos")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.horizontal)
                
                VideoHScrollView(
                    videos: viewModel.followerVideos,
                    selectedVideo: $selectedVideo,
                    onUnfollowed: { channel in
                        viewModel.unfollow(channel)
                    }
                )
            }
            #if os(tvOS)
            .focused($focusSection, equals: .vod)
            #endif
        }
        
        if !viewModel.followers.isEmpty {
            VStack(alignment: .leading) {
                Text("Following")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.horizontal)
                
                ChannelHScrollView(
                    channels: viewModel.followers,
                    onSelectedChannel: { channel in
                        NotificationCenter.default.post(
                            name: .navigateToChannel,
                            object: channel.id
                        )
                    },
                    onUnfollowed: { channel in
                        viewModel.unfollow(channel)
                    }
                )
            }
            #if os(tvOS)
            .focused($focusSection, equals: .followers)
            #endif
        }
    }
}

#Preview("Loaded state") {
    let loginViewModel = QRCodeLoginViewModel()
    loginViewModel.isUserLoggedIn = true

    return HomeView(
        channelService: PreviewChannelService(),
        liveService: PreviewLiveService(),
        videoService: PreviewVideoService(),
        loginViewModel: loginViewModel
    )
}

#Preview("Loading State") {
    let loginViewModel = QRCodeLoginViewModel()
    loginViewModel.isUserLoggedIn = true

    return HomeView(
        channelService: PreviewChannelService(),
        liveService: PreviewLiveService(),
        videoService: PreviewVideoService(),
        loginViewModel: loginViewModel
    )
}

#Preview("Empty State") {
    let loginViewModel = QRCodeLoginViewModel()
    loginViewModel.isUserLoggedIn = true

    return HomeView(
        channelService: PreviewChannelService(),
        liveService: PreviewLiveService(),
        videoService: PreviewVideoService(),
        loginViewModel: loginViewModel
    )
}

#Preview("Error State") {
    let loginViewModel = QRCodeLoginViewModel()
    loginViewModel.isUserLoggedIn = true

    return HomeView(
        channelService: PreviewChannelService(),
        liveService: PreviewLiveService(),
        videoService: PreviewVideoService(),
        loginViewModel: loginViewModel
    )
}
