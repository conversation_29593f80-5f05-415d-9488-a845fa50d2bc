//
//  CategoryLiveView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 5/17/25.
//

import SwiftUI
import SwiftData

struct CategoryLiveView: View {
    let category: UICategory
    @StateObject private var viewModel: CategoryLiveViewModel
    @State private var selectedStream: UILiveStream? = nil
    
    @Environment(\.channelService) private var channelService
    @Environment(\.dismiss) private var dismiss
    
    init(category: UICategory, categoryService: CategoryServiceProtocol) {
        self.category = category
        _viewModel = StateObject(wrappedValue: CategoryLiveViewModel(
            categoryService: categoryService,
            category: category
        ))
    }
    
    var body: some View {
        VStack {
            switch viewModel.state {
            case .idle, .loaded, .loading:
                LiveStreamVScrollView(
                    streams: viewModel.streams,
                    selectedStream: $selectedStream,
                    onLoadMore: { await viewModel.loadMoreStreamsIfNeeded() },
                    isLoading: viewModel.state.isLoading || viewModel.isLoadingMore
                )
                
            case .error(let error):
                ErrorView(title: "Error loading live streams",
                          message: error.localizedDescription,
                          isRetrying: false) {
                    Task {
                        await viewModel.retry()
                    }
                }
            }
        }
        .task {
            await viewModel.loadStreams()
        }
        .fullScreenCover(item: $selectedStream) { stream in
            LiveDetailView(stream: stream, viewModel: .init(
                channelService: channelService
            ))
        }
    }
}

#Preview {
    CategoryLiveView(
        category: StreamPreview.sampleUICategory,
        categoryService: PreviewCategoryService()
    )
} 
