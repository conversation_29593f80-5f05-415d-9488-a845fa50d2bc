//
//  CategoryView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import SwiftUI

struct CategoryView: View {
    @StateObject private var viewModel: CategoryViewModel
    @Environment(\.categoryService) private var categoryService
    @State private var selectedCategory: UICategory? = nil
    @State private var path: [UICategory] = []
    
    init(viewModel: CategoryViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var body: some View {
        NavigationStack(path: $path) {
            VStack(alignment: .leading, spacing: 8) {
                switch viewModel.state {
                case .idle,
                        .loading where viewModel.categories.isEmpty:
                    CategoryLoadingView()
                    
                case .loaded, .loading:
                    CategoryVScrollView(
                        categories: viewModel.categories,
                        selectedCategory: $selectedCategory,
                        onLoadMore: { await viewModel.loadMoreCategories() },
                        isLoading: viewModel.state.isLoading || viewModel.isLoadingMore
                    )
                    
                case .error(let error):
                    ErrorView(title: "Error loading categories",
                              message: error.localizedDescription,
                              isRetrying: false) {
                        Task {
                            await viewModel.retry()
                        }
                    }
                }
            }
            .task {
                await viewModel.loadCategories()
            }
            .navigationDestination(for: UICategory.self) { category in
                CategoryLiveView(
                    category: category,
                    categoryService: categoryService
                )
            }
            .onChange(of: selectedCategory) { _, newValue in
                if let category = newValue {
                    path.append(category)
                    selectedCategory = nil
                }
            }
        }
    }
}

#Preview {
    CategoryView(
        viewModel: CategoryViewModel(categoryService: PreviewCategoryService())
    )
}
