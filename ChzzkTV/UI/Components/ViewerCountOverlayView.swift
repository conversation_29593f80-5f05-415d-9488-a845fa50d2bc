//
//  ViewerCountOverlayView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/6/25.
//

import SwiftUI

struct ViewerCountOverlayView: View {
    let label: String
    
    var body: some View {
        HStack(spacing: Constants.viewerCountSpacing) {
            Circle()
                .fill(.red)
                .frame(width: Constants.viewerCountIndicatorSize,
                       height: Constants.viewerCountIndicatorSize)
            
            Text(label)
                .font(.caption2)
                .foregroundStyle(.white)
        }
        .padding(.horizontal, Constants.viewerCountPaddingH)
        .padding(.vertical, Constants.viewerCountPaddingV)
        .background {
            Capsule()
                .fill(.black.opacity(0.7))
        }
    }
}
