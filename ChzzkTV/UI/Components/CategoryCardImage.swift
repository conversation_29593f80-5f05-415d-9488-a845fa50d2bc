//
//  CategoryCardImage.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import SwiftUI

struct CategoryCardImage: View {
    @State private var dataUrl: URL?
    private let ratio: Double = 3/4
    let url: URL?
    
    var body: some View {
        if url != nil {
            if let file = url?.lastPathComponent,
               let fileName = file.components(separatedBy: ".").first,
               let image = UIImage(named: fileName) {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(ratio, contentMode: .fill)
            } else {
                AsyncImage(url: dataUrl) { image in
                    image
                        .resizable()
                        .aspectRatio(ratio, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(ratio, contentMode: .fill)
                }
                .clipped()
                .onDisappear {
                    dataUrl = nil
                }
                .onAppear{
                    dataUrl = url
                }
            }
        } else {
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .aspectRatio(ratio, contentMode: .fill)
        }
    }
}
