//
//  ChannelCardImage.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import SwiftUI

struct ChannelCardImage: View {
    @State private var dataUrl: URL?
    let url: URL?
    
    var body: some View {
        if url != nil {
            AsyncImage(url: dataUrl) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: Constants.channelCardSize, height: Constants.channelCardSize)
                    .clipShape(Circle())
            } placeholder: {
                Circle()
                    .foregroundColor(.gray.opacity(0.3))
                    .frame(width: Constants.channelCardSize, height: Constants.channelCardSize)
            }
            .onDisappear {
                dataUrl = nil
            }
            .onAppear{
                dataUrl = url
            }
        } else {
            Image(systemName: "person.circle")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .background(.gray.opacity(0.3))
                .hoverEffect(.highlight)
                .clipShape(Circle())
                .foregroundStyle(.gray)
        }
    }
}

#Preview {
    ChannelCardImage(url: URL(string: "https://picsum.photos/200")!)
}
