//
//  VideoVScrollView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/31/25.
//

import SwiftUI

struct VideoVScrollView: View {
    let videos: [UIVideo]
    @Binding var selectedVideo: UIVideo?
    
    @State private var containerWidth: CGFloat = 0
    
    private func columns(for width: CGFloat) -> [GridItem] {
        #if os(tvOS)
        return [GridItem(.adaptive(minimum: Constants.videoCardWidth), alignment: .top)]
        #else
        if width > 600 {
            return [
                GridItem(.flexible(), alignment: .top),
                GridItem(.flexible(), alignment: .top)
            ]
        } else {
            return [GridItem(.flexible(), alignment: .top)]
        }
        #endif
    }
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns(for: containerWidth), spacing: Constants.cardSpacing) {
                ForEach(videos) { video in
                    Button {
                        selectedVideo = video
                    } label: {
                        VideoCard(video: video, hideChannelName: true)
                            .aspectRatio(16/10, contentMode: .fit)
                    }
#if os(tvOS)
                    .buttonStyle(.card)
#else
                    .buttonStyle(.plain)
#endif
                }
            }
            .padding(.horizontal)
        }
        .containerRelativeFrame(.horizontal) { size, _ in
            Task {
                containerWidth = size
            }
            return size
        }
    }
}

#Preview {
    VideoVScrollView(
        videos: StreamPreview.createSampleUIVideos(count: 20),
        selectedVideo: .constant(nil)
    )
}
