//
//  ChannelImage.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import SwiftUI

struct ChannelImage: View {
    @State private var dataUrl: URL?
    let url: URL
    
    var body: some View {
        AsyncImage(url: dataUrl) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Circle()
                .fill(Color.gray.opacity(0.3))
        }
        .frame(width: Constants.channelImageSize, height: Constants.channelImageSize)
        .clipShape(Circle())
        .onDisappear {
            dataUrl = nil
        }
        .onAppear{
            dataUrl = url
        }
    }
}
