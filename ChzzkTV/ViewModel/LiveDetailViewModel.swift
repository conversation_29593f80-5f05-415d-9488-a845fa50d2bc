//
//  LiveDetailViewModel.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/16/25.
//

import SwiftUI
import AVKit
import MediaPlayer

@MainActor
class LiveDetailViewModel: NSObject, ObservableObject {
    @Published private(set) var isLoading = false
    @Published private(set) var error: Error?
    @Published var liveStream: UILiveStream?
    @Published private(set) var player: AVPlayer?
    @Published private(set) var availableQualities: [VideoQuality] = []
    @Published private(set) var currentQuality: VideoQuality?
    
    private let channelService: ChannelServiceProtocol
    private var playerItem: AVPlayerItem?
    private var timeObserver: Any?
    private var metadataItems: [AVMetadataItem]?
    
    init(channelService: ChannelServiceProtocol) {
        self.channelService = channelService
        super.init()
        setupRemoteTransportControls()
    }
    
    private func setupRemoteTransportControls() {
        // Get the shared MPRemoteCommandCenter
        let commandCenter = MPRemoteCommandCenter.shared()
        
        // Add handler for Play Command
        commandCenter.playCommand.addTarget { [weak self] event in
            guard let self = self else { return .commandFailed }
            self.player?.play()
            return .success
        }
        
        // Add handler for Pause Command
        commandCenter.pauseCommand.addTarget { [weak self] event in
            guard let self = self else { return .commandFailed }
            self.player?.pause()
            return .success
        }
        
        // Enable playback commands
        commandCenter.playCommand.isEnabled = true
        commandCenter.pauseCommand.isEnabled = true
        commandCenter.togglePlayPauseCommand.isEnabled = true
    }
    
    private func createMetadataItems(
        for liveStream: UILiveStream,
        channel: UIChannel,
        imageData: Data?
    ) -> [AVMetadataItem] {
        let mapping: [AVMetadataIdentifier: Any] = [
            .commonIdentifierTitle: liveStream.title,
            .iTunesMetadataTrackSubTitle: liveStream.channel?.name ?? "",
            .commonIdentifierArtwork: imageData ?? Data(),
            .commonIdentifierDescription:
"""
\(liveStream.category ?? "")
\(liveStream.relativeDate)
\(liveStream.viewerCountFormatted)
\(liveStream.channel?.followerCountFormatted ?? "")
"""
        ]
        return mapping.compactMap { createMetadataItem(for: $0, value: $1) }
    }
    private func createMetadataItem(for identifier: AVMetadataIdentifier,
                                    value: Any) -> AVMetadataItem {
        let item = AVMutableMetadataItem()
        item.identifier = identifier
        item.value = value as? NSCopying & NSObjectProtocol
        // Specify "und" to indicate an undefined language.
        item.extendedLanguageTag = "und"
        return item.copy() as! AVMetadataItem
    }
    
    func loadStreamDetails(channelId: String) async {
        isLoading = true
        error = nil
        liveStream = nil
        availableQualities = []
        currentQuality = nil
        
        do {
            guard let liveStream = try await channelService.getLiveWithPlaybackUrl(id: channelId),
                  let qualities = liveStream.qualities,
                  let url = qualities.first?.url else {
                throw NSError(domain: "StreamError", code: -1, userInfo: [NSLocalizedDescriptionKey: "No playback url"])
            }
            
            availableQualities = qualities
            currentQuality = availableQualities.first
            
            setupPlayer(with: url)
            
            let channel = try await channelService.getChannel(id: channelId)
            let imageData = await channel.imageData()
            
            let metadataItems = createMetadataItems(for: liveStream, channel: channel, imageData: imageData)
            playerItem?.externalMetadata = metadataItems
            self.metadataItems = metadataItems
        } catch {
            self.error = error
        }
        
        isLoading = false
    }
    
    private func setupPlayer(with url: URL) {
        // Create asset with options for better media processing
        let asset = AVURLAsset(url: url, options: [
            AVURLAssetPreferPreciseDurationAndTimingKey: true
        ])
        
        // Create player item with optimized configuration
        playerItem = AVPlayerItem(asset: asset)
        playerItem?.audioTimePitchAlgorithm = .spectral
        
        // Configure player with enhanced settings
        player = AVPlayer(playerItem: playerItem)
        player?.allowsExternalPlayback = true
        player?.preventsDisplaySleepDuringVideoPlayback = true
        player?.automaticallyWaitsToMinimizeStalling = true
        
        // Update Now Playing Info
        updateNowPlayingInfo()
        
        player?.play()
    }
    
    private func updateNowPlayingInfo() {
        var nowPlayingInfo = [String: Any]()
        
        // Set the title
        nowPlayingInfo[MPMediaItemPropertyTitle] = liveStream?.title ?? "Live Stream"
        
        // Set media type
        nowPlayingInfo[MPNowPlayingInfoPropertyMediaType] = MPMediaType.anyVideo.rawValue
        
        // Set playback rate
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = player?.rate ?? 1.0
        
        nowPlayingInfo[MPNowPlayingInfoPropertyIsLiveStream] = true
        
        // Update the now playing info
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
    
    func togglePlayback() {
        guard let player = player else { return }
        if player.rate > 0 {
            player.pause()
        } else {
            player.play()
        }
        updateNowPlayingInfo()
    }
    
    func cleanUp() {
        print("LiveDetailViewModel: cleanup started")
        
        // Set loading to false and clear errors
        isLoading = false
        error = nil
        
        // Clear stream details
        liveStream = nil
        
        // Remove time observer
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        
        // Remove notifications
        NotificationCenter.default.removeObserver(self)
        
        // Clear media playback info
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nil
        
        // Disable remote commands to avoid retaining the player
        let commandCenter = MPRemoteCommandCenter.shared()
        commandCenter.playCommand.isEnabled = false
        commandCenter.pauseCommand.isEnabled = false
        commandCenter.togglePlayPauseCommand.isEnabled = false
        
        // Clean up player
        player?.pause()
        player?.replaceCurrentItem(with: nil)
        player = nil
        playerItem = nil
        
        print("LiveDetailViewModel: cleanup completed")
    }
    
    func resetError() {
        error = nil
    }
    
    func changeQuality(_ quality: VideoQuality) async {
        guard quality != currentQuality else { return }
        
        let currentTime = player?.currentTime()
        
        // Clean up existing player
        cleanUp()
        
        // Setup new player with selected quality
        setupPlayer(with: quality.url)
        
        playerItem?.externalMetadata = self.metadataItems ?? []
        
        // Restore playback position
        if let time = currentTime {
            await player?.seek(to: time)
        }
        
        currentQuality = quality
    }
}
