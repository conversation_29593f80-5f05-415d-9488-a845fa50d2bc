//
//  CategoryLiveViewModel.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/5/25.
//

import SwiftUI
import SwiftData

@MainActor
class CategoryLiveViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var streams: [UILiveStream] = []
    @Published var state: LoadingState = .idle
    @Published var isLoadingMore: Bool = false
    private var hasNextPage: Bool = true
    
    // MARK: - Dependencies
    private let categoryService: CategoryServiceProtocol
    private let category: UICategory
    
    // MARK: - State Enum
    enum LoadingState {
        case idle
        case loading
        case loaded
        case error(Error)
        
        var isLoading: Bool {
            if case .loading = self { return true }
            return false
        }
        
        var error: Error? {
            if case .error(let error) = self { return error }
            return nil
        }
    }
    
    // MARK: - Initialization
    init(categoryService: CategoryServiceProtocol,
         category: UICategory) {
        self.categoryService = categoryService
        self.category = category
    }
    
    // MARK: - Public Methods
    func loadStreams() async {
        guard !state.isLoading else { return }
        
        state = .loading
        
        do {
            let (streams, hasNext) = try await categoryService.getLivesInCategory(category, usePagination: false)
            self.streams = streams
            self.hasNextPage = hasNext
            
            state = .loaded
        } catch {
            state = .error(error)
        }
    }
    
    func loadMoreStreamsIfNeeded() async {
        guard !state.isLoading && !isLoadingMore && hasNextPage else { return }
        
        isLoadingMore = true
        
        do {
            let (newStreams, hasNext) = try await categoryService.getLivesInCategory(category, usePagination: true)
            
            // Filter out streams that already exist in the current list
            let uniqueNewStreams = newStreams.filter { newStream in
                !self.streams.contains { existingStream in
                    existingStream.id == newStream.id
                }
            }
            
            self.streams.append(contentsOf: uniqueNewStreams)
            self.hasNextPage = hasNext
            
            isLoadingMore = false
        } catch {
            isLoadingMore = false
            // We don't set state to error here to preserve the currently loaded content
        }
    }
    
    func retry() async {
        await loadStreams()
    }
}
