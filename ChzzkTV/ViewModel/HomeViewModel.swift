import Foundation
import SwiftData
import <PERSON><PERSON>

@MainActor
class HomeViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var state: LoadingState = .idle
    @Published var followerLives: [UILiveStream] = []
    @Published var followers: [UIChannel] = []
    @Published var followerVideos: [UIVideo] = []
    
    // MARK: - Dependencies
    private let channelService: ChannelServiceProtocol
    private let liveService: LiveServiceProtocol
    private let videoService: VideoServiceProtocol
    private let loginViewModel: QRCodeLoginViewModel
    
    // MARK: - State Enum
    enum LoadingState {
        case idle
        case loading
        case loaded
        case error(Error)
        
        var isLoading: Bool {
            if case .loading = self { return true }
            return false
        }
        
        var error: Error? {
            if case .error(let error) = self { return error }
            return nil
        }
    }
    
    // MARK: - Initialization
    init(
        channelService: ChannelServiceProtocol,
        liveService: LiveServiceProtocol,
        videoService: VideoServiceProtocol,
        loginViewModel: QRCodeLoginViewModel
    ) {
        self.channelService = channelService
        self.liveService = liveService
        self.videoService = videoService
        self.loginViewModel = loginViewModel
    }
    
    // MARK: - Public Methods
    func loadContent() async {
        if case .loading = state { return }

        state = .loading

        do {
            // First, verify user status to ensure cookies are updated
            await loginViewModel.verifyUserStatus(forValidation: true)

            // Only proceed with API calls if user is logged in
            guard loginViewModel.isUserLoggedIn else {
                state = .error(NSError(domain: "ChzzkTV", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not logged in"]))
                return
            }

            async let livesTask = liveService.getFollowingLives()
            async let videosTask = videoService.getFollowingVideos()
            async let channelsTask = channelService.getFollowingChannels()

            (followerLives, followerVideos, followers) = try await (livesTask, videosTask, channelsTask)

            state = .loaded
        } catch {
            state = .error(error)
        }
    }
    
    func loadVideos() async {
        do {
            // Verify user status before loading videos
            await loginViewModel.verifyUserStatus(forValidation: true)

            guard loginViewModel.isUserLoggedIn else {
                print("User not logged in, skipping video load")
                return
            }

            followerVideos = try await videoService.getFollowingVideos()
        } catch {
            print("Failed to load videos: \(error.localizedDescription)")
        }
    }
    
    var isEmpty: Bool {
        followers.isEmpty && followerLives.isEmpty && followerVideos.isEmpty
    }
    
    func unfollow(_ channel: UIChannel) {
        followers = followers.filter({ $0.id != channel.id })
        followerLives = followerLives.filter({ $0.channel?.id != channel.id })
        followerVideos = followerVideos.filter({ $0.channel?.id != channel.id })
    }
}
