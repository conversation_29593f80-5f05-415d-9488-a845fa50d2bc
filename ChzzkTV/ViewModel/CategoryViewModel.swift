import Foundation
import SwiftUI

@MainActor
class CategoryViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var categories: [UICategory] = []
    @Published var state: LoadingState = .idle
    @Published var isLoadingMore: Bool = false
    private var hasNextPage: Bool = true
    
    // MARK: - Dependencies
    private let categoryService: CategoryServiceProtocol
    
    // MARK: - State Enum
    enum LoadingState {
        case idle
        case loading
        case loaded
        case error(Error)
        
        var isLoading: Bool {
            if case .loading = self { return true }
            return false
        }
        
        var error: Error? {
            if case .error(let error) = self { return error }
            return nil
        }
    }
    
    // MARK: - Initialization
    init(categoryService: CategoryServiceProtocol) {
        self.categoryService = categoryService
    }
    
    // MARK: - Public Methods
    func loadCategories() async {
        guard !state.isLoading else { return }
        
        state = .loading
        
        do {
            let (categories, hasNext) = try await categoryService.getCategories(usePagination: false)
            self.categories = categories
            self.hasNextPage = hasNext
            
            state = .loaded
        } catch {
            state = .error(error)
        }
    }
    
    func loadMoreCategories() async {
        guard !state.isLoading && !isLoadingMore && hasNextPage else { return }
        
        isLoadingMore = true
        
        do {
            let (newCategories, hasNext) = try await categoryService.getCategories(usePagination: true)
            
            // Filter out categories that already exist in the current list
            let uniqueNewCategories = newCategories.filter { newCategory in
                !self.categories.contains { existingCategory in
                    existingCategory.id == newCategory.id
                }
            }
            
            self.categories.append(contentsOf: uniqueNewCategories)
            self.hasNextPage = hasNext
            
            isLoadingMore = false
        } catch {
            isLoadingMore = false
            // We don't set state to error here to preserve the currently loaded content
            // Could optionally show a toast or other non-blocking error indicator
        }
    }
    
    func retry() async {
        await loadCategories()
    }
}
