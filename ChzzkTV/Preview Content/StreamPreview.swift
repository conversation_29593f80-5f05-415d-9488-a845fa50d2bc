import Foundation

/// Helper for generating preview data
struct StreamPreview {
    static let sampleUIChannel = UIChannel(
        id: "sample123",
        name: "Sample Channel",
        imageUrl: URL(string: "https://picsum.photos/200")!,
        description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
        isVerified: true,
        followerCount: 12500,
        isLive: true)
    
    static let sampleUILiveStream = UILiveStream(
        id: 12345,
        title: "Sample Live Stream with a very long title that will wrap to multiple lines and test truncation",
        imageUrl: URL(string:"https://picsum.photos/400/250")!,
        viewerCount: 1234,
        timestamp: Date.now.timeIntervalSince1970 - 3600,
        category: "Gaming",
        channel: sampleUIChannel,
    )
    
    static let sampleUIVideo = UIVideo(
        id: 67890,
        uid: "videoId",
        title: "Sample Video Title That Is Also Very Long To Test Truncation In Cards",
        imageUrl: URL(string: "https://picsum.photos/400/250")!,
        duration: 3600,
        timestamp: Date.now.timeIntervalSince1970 - 86400,
        category: "League of Legends",
        channel: sampleUIChannel,
        progress: 0.5
    )
    
    static let sampleUICategory = UICategory(
        id: "1",
        type: "GAME",
        value: "League of Legends",
        imageUrl: URL(string: "https://picsum.photos/300/400"),
        channelCount: 200,
        viewerCount: 12345,
        isNew: true
    )
}

extension StreamPreview {
    static func createSampleUIChannel(index: Int) -> UIChannel {
        UIChannel(
            id: "sample\(index)",
            name: "Channel \(index)",
            imageUrl: URL(string: "https://picsum.photos/\(index % 3)00")!,
            isVerified: index % 2 == 0,
            followerCount: 1000 * (index + 1),
            isLive: index % 2 == 0
        )
    }
    
    static func createSampleUIChannels(count: Int) -> [UIChannel] {
        return (0..<count).map { i in
            return createSampleUIChannel(index: i)
        }
    }
    
    static func createSampleUILiveStreams(count: Int) -> [UILiveStream] {
        return (0..<count).map { i in
            return UILiveStream(
                id: i,
                title: "Live Stream \(i)",
                imageUrl: sampleUILiveStream.imageUrl,
                viewerCount: 1000 * (i + 1),
                timestamp: Date().timeIntervalSince1970 - TimeInterval(3600 * (i + 1)),
                category: "Category \(i % 3)",
                channel: createSampleUIChannel(index: i)
            )
        }
    }
    
    static func createSampleUIVideos(count: Int) -> [UIVideo] {
        return (0..<count).map { i in
            return UIVideo(
                id: i,
                uid: "video-\(i)",
                title: "Video \(i) Title",
                imageUrl: sampleUIVideo.imageUrl,
                duration: 3600 * i,
                timestamp: Date().timeIntervalSince1970 - TimeInterval(3600 * (i + 1)),
                category: "Category \(i)",
                channel: createSampleUIChannel(index: i),
                progress: nil
            )
        }
    }
    
    static func createSampleUICategories(count: Int) -> [UICategory] {
        return (0..<count).map { i in
            UICategory(
                id: "category-\(i)",
                type: "Type \(i)",
                value: "Cateogry \(i)",
                imageUrl: URL(string: "https://picsum.photos/300/400")!,
                channelCount: 100 * (i + 1),
                viewerCount: 10000 * (i + 1),
                isNew: i % 2 == 0
            )
        }
    }
}
