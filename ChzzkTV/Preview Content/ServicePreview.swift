//
//  ServicePreview.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/29/25.
//

import SwiftData
import AVKit

class PreviewChannelService: ChannelServiceProtocol {
    func toggleFollow(channelId: String) async -> Bool {
        true
    }
    
    func isFollowing(channelId: String) -> Bool {
        true
    }
    
    func getLive(id: String) async throws -> UILiveStream? {
        StreamPreview.sampleUILiveStream
    }
    
    func getLatestVideo(id: String) async throws -> UIVideo? {
        StreamPreview.sampleUIVideo
    }
    
    func getVideos(id: String, page: Int, size: Int) async throws -> ([UIVideo]?, Int?) {
        (StreamPreview.createSampleUIVideos(count: 5), 1)
    }
    
    func getChannel(id: String) async throws -> UIChannel {
        StreamPreview.sampleUIChannel
    }
    
    func getLiveWithPlaybackUrl(id: String) async throws -> UILiveStream? {
        StreamPreview.sampleUILiveStream
    }
    
    func getFollowingChannels() async throws -> [UIChannel] {
        StreamPreview.createSampleUIChannels(count: 10)
    }
}

class PreviewLiveService: LiveServiceProtocol {
    func getAllLives(type: LiveSortType) async throws -> ([UILiveStream], Bool) {
        (StreamPreview.createSampleUILiveStreams(count: 10), false)
    }
    
    func getNextAllLives(type: LiveSortType) async throws -> ([UILiveStream], Bool) {
        ([], false)
    }
    
    func getFollowingLives() async throws -> [UILiveStream] {
        []
    }
}

class PreviewVideoService: VideoServiceProtocol {
    func getAvailableQualities(for videoId: Int) async throws -> [VideoQuality] {
        []
    }
    
    func getVideo(_ videoId: Int) async throws -> UIVideo {
        StreamPreview.sampleUIVideo
    }
    
    func getFollowingVideos() async throws -> [UIVideo] {
        StreamPreview.createSampleUIVideos(count: 10)
    }
    
    func createAsset(for videoId: Int, quality: VideoQuality) async throws -> AVURLAsset {
        AVURLAsset(url: URL(string: "")!)
    }
    
    func clear() {
        
    }
    
    func watchEvent(time: Int, video: UIVideo, event: VideoPlayEvent) async throws {
        
    }
}

class PreviewSearchService: SearchServiceProtocol {
    func getChannels(keyword: String) async throws -> [UIChannel] {
        StreamPreview.createSampleUIChannels(count: 20)
    }
    
    func getSuggestions(keyword: String) async throws -> [String] {
        ["A", "B", "C"]
    }
}

class PreviewCategoryService: CategoryServiceProtocol {
    func getCategories(usePagination: Bool = false) async throws -> ([UICategory], Bool) {
        if usePagination {
            return ([], false)
        } else {
            return (StreamPreview.createSampleUICategories(count: 20), false)
        }
    }
    
    func getLivesInCategory(_ category: UICategory, usePagination: Bool = false) async throws -> ([UILiveStream], Bool) {
        if usePagination {
            return ([], false)
        } else {
            return (StreamPreview.createSampleUILiveStreams(count: 20), false)
        }
    }
}

