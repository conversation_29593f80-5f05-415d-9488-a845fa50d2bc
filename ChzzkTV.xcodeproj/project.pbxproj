// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		554375C12D86947500F6FE2E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 554375A52D86947400F6FE2E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 554375AC2D86947400F6FE2E;
			remoteInfo = ChzzkTV;
		};
		554375CB2D86947500F6FE2E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 554375A52D86947400F6FE2E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 554375AC2D86947400F6FE2E;
			remoteInfo = ChzzkTV;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		554375AD2D86947400F6FE2E /* ChzzkTV.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ChzzkTV.app; sourceTree = BUILT_PRODUCTS_DIR; };
		554375C02D86947500F6FE2E /* ChzzkTVTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ChzzkTVTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		554375CA2D86947500F6FE2E /* ChzzkTVUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ChzzkTVUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		559C46F72E26205400C3150A /* Exceptions for "ChzzkTV" folder in "ChzzkTV" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 554375AC2D86947400F6FE2E /* ChzzkTV */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		554375AF2D86947400F6FE2E /* ChzzkTV */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				559C46F72E26205400C3150A /* Exceptions for "ChzzkTV" folder in "ChzzkTV" target */,
			);
			path = ChzzkTV;
			sourceTree = "<group>";
		};
		554375C32D86947500F6FE2E /* ChzzkTVTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ChzzkTVTests;
			sourceTree = "<group>";
		};
		554375CD2D86947500F6FE2E /* ChzzkTVUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ChzzkTVUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		554375AA2D86947400F6FE2E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		554375BD2D86947500F6FE2E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		554375C72D86947500F6FE2E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		554375A42D86947400F6FE2E = {
			isa = PBXGroup;
			children = (
				554375AF2D86947400F6FE2E /* ChzzkTV */,
				554375C32D86947500F6FE2E /* ChzzkTVTests */,
				554375CD2D86947500F6FE2E /* ChzzkTVUITests */,
				554375AE2D86947400F6FE2E /* Products */,
			);
			sourceTree = "<group>";
		};
		554375AE2D86947400F6FE2E /* Products */ = {
			isa = PBXGroup;
			children = (
				554375AD2D86947400F6FE2E /* ChzzkTV.app */,
				554375C02D86947500F6FE2E /* ChzzkTVTests.xctest */,
				554375CA2D86947500F6FE2E /* ChzzkTVUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		554375AC2D86947400F6FE2E /* ChzzkTV */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 554375D42D86947500F6FE2E /* Build configuration list for PBXNativeTarget "ChzzkTV" */;
			buildPhases = (
				554375A92D86947400F6FE2E /* Sources */,
				554375AA2D86947400F6FE2E /* Frameworks */,
				554375AB2D86947400F6FE2E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				554375AF2D86947400F6FE2E /* ChzzkTV */,
			);
			name = ChzzkTV;
			productName = ChzzkTV;
			productReference = 554375AD2D86947400F6FE2E /* ChzzkTV.app */;
			productType = "com.apple.product-type.application";
		};
		554375BF2D86947500F6FE2E /* ChzzkTVTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 554375D72D86947500F6FE2E /* Build configuration list for PBXNativeTarget "ChzzkTVTests" */;
			buildPhases = (
				554375BC2D86947500F6FE2E /* Sources */,
				554375BD2D86947500F6FE2E /* Frameworks */,
				554375BE2D86947500F6FE2E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				554375C22D86947500F6FE2E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				554375C32D86947500F6FE2E /* ChzzkTVTests */,
			);
			name = ChzzkTVTests;
			productName = ChzzkTVTests;
			productReference = 554375C02D86947500F6FE2E /* ChzzkTVTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		554375C92D86947500F6FE2E /* ChzzkTVUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 554375DA2D86947500F6FE2E /* Build configuration list for PBXNativeTarget "ChzzkTVUITests" */;
			buildPhases = (
				554375C62D86947500F6FE2E /* Sources */,
				554375C72D86947500F6FE2E /* Frameworks */,
				554375C82D86947500F6FE2E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				554375CC2D86947500F6FE2E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				554375CD2D86947500F6FE2E /* ChzzkTVUITests */,
			);
			name = ChzzkTVUITests;
			productName = ChzzkTVUITests;
			productReference = 554375CA2D86947500F6FE2E /* ChzzkTVUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		554375A52D86947400F6FE2E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					554375AC2D86947400F6FE2E = {
						CreatedOnToolsVersion = 16.2;
					};
					554375BF2D86947500F6FE2E = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 554375AC2D86947400F6FE2E;
					};
					554375C92D86947500F6FE2E = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 554375AC2D86947400F6FE2E;
					};
				};
			};
			buildConfigurationList = 554375A82D86947400F6FE2E /* Build configuration list for PBXProject "ChzzkTV" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ko,
			);
			mainGroup = 554375A42D86947400F6FE2E;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 554375AE2D86947400F6FE2E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				554375AC2D86947400F6FE2E /* ChzzkTV */,
				554375BF2D86947500F6FE2E /* ChzzkTVTests */,
				554375C92D86947500F6FE2E /* ChzzkTVUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		554375AB2D86947400F6FE2E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		554375BE2D86947500F6FE2E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		554375C82D86947500F6FE2E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		554375A92D86947400F6FE2E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		554375BC2D86947500F6FE2E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		554375C62D86947500F6FE2E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		554375C22D86947500F6FE2E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 554375AC2D86947400F6FE2E /* ChzzkTV */;
			targetProxy = 554375C12D86947500F6FE2E /* PBXContainerItemProxy */;
		};
		554375CC2D86947500F6FE2E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 554375AC2D86947400F6FE2E /* ChzzkTV */;
			targetProxy = 554375CB2D86947500F6FE2E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		554375D22D86947500F6FE2E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		554375D32D86947500F6FE2E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
			};
			name = Release;
		};
		554375D52D86947500F6FE2E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				"ASSETCATALOG_COMPILER_APPICON_NAME[sdk=appletvos*]" = "Brand Assets";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = "";
				CODE_SIGN_ENTITLEMENTS = ChzzkTV/ChzzkTV.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 15;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ChzzkTV/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "지지직";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.video";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.ChzzkTV;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "appletvos appletvsimulator iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,3";
				TVOS_DEPLOYMENT_TARGET = 18.0;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		554375D62D86947500F6FE2E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				"ASSETCATALOG_COMPILER_APPICON_NAME[sdk=appletvos*]" = "Brand Assets";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = "";
				CODE_SIGN_ENTITLEMENTS = ChzzkTV/ChzzkTV.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 15;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ChzzkTV/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "지지직";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.video";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.ChzzkTV;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "appletvos appletvsimulator iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,3";
				TVOS_DEPLOYMENT_TARGET = 18.0;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		554375D82D86947500F6FE2E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.ChzzkTVTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ChzzkTV.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/ChzzkTV";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		554375D92D86947500F6FE2E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.ChzzkTVTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ChzzkTV.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/ChzzkTV";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		554375DB2D86947500F6FE2E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.ChzzkTVUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = ChzzkTV;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		554375DC2D86947500F6FE2E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.ChzzkTVUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = ChzzkTV;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		554375A82D86947400F6FE2E /* Build configuration list for PBXProject "ChzzkTV" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				554375D22D86947500F6FE2E /* Debug */,
				554375D32D86947500F6FE2E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		554375D42D86947500F6FE2E /* Build configuration list for PBXNativeTarget "ChzzkTV" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				554375D52D86947500F6FE2E /* Debug */,
				554375D62D86947500F6FE2E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		554375D72D86947500F6FE2E /* Build configuration list for PBXNativeTarget "ChzzkTVTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				554375D82D86947500F6FE2E /* Debug */,
				554375D92D86947500F6FE2E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		554375DA2D86947500F6FE2E /* Build configuration list for PBXNativeTarget "ChzzkTVUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				554375DB2D86947500F6FE2E /* Debug */,
				554375DC2D86947500F6FE2E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 554375A52D86947400F6FE2E /* Project object */;
}
